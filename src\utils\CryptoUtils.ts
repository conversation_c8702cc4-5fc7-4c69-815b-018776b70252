import RNSimpleCrypto from 'react-native-simple-crypto';

const { AES, utils } = RNSimpleCrypto;

// Khóa AES-256 (32 byte)
const keyHex = 'd6T2eRAR35158B5dI61Tc#2#E6ITb!86';

export async function encrypt(text: string): Promise<string> {
  const keyBuffer = utils.convertHexToArrayBuffer(keyHex);
  const ivBuffer = await utils.randomBytes(16); // 16 byte IV
  const textBuffer = utils.convertUtf8ToArrayBuffer(text);

  const encryptedBuffer = await AES.encrypt(textBuffer, keyBuffer, ivBuffer);

  // Gộp IV + encrypted
  //concat 2 buffer
  const combined = new Uint8Array(ivBuffer.byteLength + encryptedBuffer.byteLength);
  combined.set(new Uint8Array(ivBuffer));
  combined.set(new Uint8Array(encryptedBuffer), ivBuffer.byteLength);

  // Convert to base64 để lưu
  return utils.convertArrayBufferToBase64(combined);
}

export async function decrypt(encryptedBase64: string): Promise<string> {
  const keyBuffer = utils.convertHexToArrayBuffer(keyHex);
  const combinedBuffer = utils.convertBase64ToArrayBuffer(encryptedBase64);

  // Cắt IV (16 byte đầu) và phần còn lại là encrypted data
  const ivBuffer = combinedBuffer.slice(0, 16);
  const encryptedBuffer = combinedBuffer.slice(16);

  const decryptedBuffer = await AES.decrypt(encryptedBuffer, keyBuffer, ivBuffer);
  return utils.convertArrayBufferToUtf8(decryptedBuffer);
}
